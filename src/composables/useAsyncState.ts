import { ref, type Ref } from 'vue'

export interface AsyncState<T> {
  data: Ref<T | null>
  loading: Ref<boolean>
  error: Ref<Error | null>
}

export function useAsyncState<T = unknown>(
  initialData: T | null = null
): AsyncState<T> & {
  execute: (promise: Promise<T>) => Promise<T>
  reset: () => void
} {
  const data = ref<T | null>(initialData)
  const loading = ref(false)
  const error = ref<Error | null>(null)

  const execute = async (promise: Promise<T>): Promise<T> => {
    loading.value = true
    error.value = null

    try {
      const result = await promise
      data.value = result
      return result
    } catch (e) {
      error.value = e instanceof Error ? e : new Error(String(e))
      throw e
    } finally {
      loading.value = false
    }
  }

  const reset = () => {
    data.value = initialData
    loading.value = false
    error.value = null
  }

  return {
    data,
    loading,
    error,
    execute,
    reset,
  }
}