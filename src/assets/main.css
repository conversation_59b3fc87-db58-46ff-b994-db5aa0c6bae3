@import '../styles/design-tokens.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  /* ===================================
     基础排版样式 (Typography)
     =================================== */

  /* 标题样式 */
  .text-h1 {
    font-size: var(--font-size-4xl);
    line-height: var(--line-height-tight);
    font-weight: var(--font-weight-bold);
    letter-spacing: -0.02em;
  }

  .text-h2 {
    font-size: var(--font-size-3xl);
    line-height: var(--line-height-tight);
    font-weight: var(--font-weight-bold);
    letter-spacing: -0.01em;
  }

  .text-h3 {
    font-size: var(--font-size-2xl);
    line-height: var(--line-height-normal);
    font-weight: var(--font-weight-semibold);
  }

  .text-h4 {
    font-size: var(--font-size-xl);
    line-height: var(--line-height-normal);
    font-weight: var(--font-weight-semibold);
  }

  .text-h5 {
    font-size: var(--font-size-lg);
    line-height: var(--line-height-normal);
    font-weight: var(--font-weight-medium);
  }

  /* 正文样式 */
  .text-body {
    font-size: var(--font-size-base);
    line-height: var(--line-height-relaxed);
    font-weight: var(--font-weight-normal);
  }

  .text-body-lg {
    font-size: var(--font-size-lg);
    line-height: var(--line-height-relaxed);
    font-weight: var(--font-weight-normal);
  }

  .text-body-sm {
    font-size: var(--font-size-sm);
    line-height: var(--line-height-normal);
    font-weight: var(--font-weight-normal);
  }

  /* 辅助文本样式 */
  .text-caption {
    font-size: var(--font-size-xs);
    line-height: var(--line-height-normal);
    font-weight: var(--font-weight-normal);
    color: var(--color-text-secondary);
  }

  .text-overline {
    font-size: var(--font-size-xs);
    line-height: var(--line-height-normal);
    font-weight: var(--font-weight-semibold);
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  /* ===================================
     统一的交互状态样式
     =================================== */

  /* 基础交互元素 */
  .interactive {
    transition-property: color, background-color, border-color, transform, box-shadow;
    transition-duration: var(--duration-fast);
    transition-timing-function: var(--ease-in-out);
  }

  /* 悬停状态 */
  .interactive:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
  }

  /* 聚焦状态 */
  .interactive:focus:not(:disabled) {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
  }

  /* 激活状态 */
  .interactive:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
  }

  /* 禁用状态 */
  .interactive:disabled,
  .interactive.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
  }
}

@layer components {
  .container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  /* ===================================
     实用工具类 (Utility Classes)
     =================================== */

  /* 间距工具类 */
  .space-1 {
    margin: var(--space-1);
  }
  .space-2 {
    margin: var(--space-2);
  }
  .space-3 {
    margin: var(--space-3);
  }
  .space-4 {
    margin: var(--space-4);
  }
  .space-5 {
    margin: var(--space-5);
  }
  .space-6 {
    margin: var(--space-6);
  }
  .space-7 {
    margin: var(--space-7);
  }
  .space-8 {
    margin: var(--space-8);
  }

  /* 内边距工具类 */
  .pad-1 {
    padding: var(--space-1);
  }
  .pad-2 {
    padding: var(--space-2);
  }
  .pad-3 {
    padding: var(--space-3);
  }
  .pad-4 {
    padding: var(--space-4);
  }
  .pad-5 {
    padding: var(--space-5);
  }
  .pad-6 {
    padding: var(--space-6);
  }
  .pad-7 {
    padding: var(--space-7);
  }
  .pad-8 {
    padding: var(--space-8);
  }

  /* 圆角工具类 */
  .radius-sm {
    border-radius: var(--radius-sm);
  }
  .radius-base {
    border-radius: var(--radius-base);
  }
  .radius-md {
    border-radius: var(--radius-md);
  }
  .radius-lg {
    border-radius: var(--radius-lg);
  }
  .radius-xl {
    border-radius: var(--radius-xl);
  }
  .radius-full {
    border-radius: var(--radius-full);
  }

  /* 阴影工具类 */
  .shadow-sm {
    box-shadow: var(--shadow-sm);
  }
  .shadow-base {
    box-shadow: var(--shadow-base);
  }
  .shadow-md {
    box-shadow: var(--shadow-md);
  }
  .shadow-lg {
    box-shadow: var(--shadow-lg);
  }
  .shadow-xl {
    box-shadow: var(--shadow-xl);
  }

  /* 颜色工具类 */
  .text-primary {
    color: var(--color-text-primary);
  }
  .text-secondary {
    color: var(--color-text-secondary);
  }
  .text-tertiary {
    color: var(--color-text-tertiary);
  }
  .text-disabled {
    color: var(--color-text-disabled);
  }

  .bg-primary {
    background-color: var(--color-primary);
  }
  .bg-secondary {
    background-color: var(--color-secondary);
  }
  .bg-tertiary {
    background-color: var(--color-tertiary);
  }

  /* 边框工具类 */
  .border-default {
    border-color: var(--color-border);
  }
  .border-hover {
    border-color: var(--color-border-hover);
  }
  .border-focus {
    border-color: var(--color-border-focus);
  }
}

/* Custom Animations */
@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
  100% {
    transform: translateY(0px);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes rotateIn {
  from {
    opacity: 0;
    transform: rotate(-15deg) scale(0.9);
  }
  to {
    opacity: 1;
    transform: rotate(0) scale(1);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-pulse-slow {
  animation: pulse 3s ease-in-out infinite;
}

.animate-shimmer {
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out forwards;
}

.animate-rotate-in {
  animation: rotateIn 0.5s ease-out forwards;
}

/* 玻璃态效果 */
.glass-effect {
  @apply bg-white bg-opacity-10 backdrop-filter backdrop-blur-lg border border-white border-opacity-20 shadow-lg;
}

/* 渐变背景 */
.gradient-bg {
  @apply bg-gradient-to-br from-primary-500 via-tertiary-500 to-secondary-500;
}

/* 悬停效果 */
.hover-lift {
  @apply transition-all duration-300 transform hover:-translate-y-1 hover:shadow-lg;
}

/* Smooth Scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  @apply bg-primary-500;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-primary-600;
}
