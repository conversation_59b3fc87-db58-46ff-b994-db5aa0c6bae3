<template>
  <component
    :is="props.as"
    :class="cn(buttonVariants({ variant: props.variant, size: props.size }), props.class)"
    v-bind="$attrs"
  >
    <slot />
  </component>
</template>

<script setup lang="ts">
import { cn } from '@/lib/utils'
import { cva } from 'class-variance-authority'

const buttonVariants = cva(
  'inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg text-sm font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-600 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0',
  {
    variants: {
      variant: {
        default:
          'bg-gradient-to-r from-primary-600 to-primary-700 text-white shadow-lg hover:shadow-xl hover:from-primary-700 hover:to-primary-800 hover:-translate-y-0.5',
        destructive:
          'bg-gradient-to-r from-red-600 to-red-700 text-white shadow-lg hover:shadow-xl hover:from-red-700 hover:to-red-800',
        outline:
          'border-2 border-primary-200 bg-white/10 backdrop-blur-sm text-primary-700 hover:bg-primary-50 hover:border-primary-300 hover:shadow-md',
        secondary:
          'bg-gradient-to-r from-secondary-100 to-secondary-200 text-secondary-700 shadow-md hover:shadow-lg hover:from-secondary-200 hover:to-secondary-300',
        ghost: 'text-primary-700 hover:bg-primary-50 hover:text-primary-800',
        glass:
          'bg-white/10 backdrop-blur-md border border-white/20 text-white hover:bg-white/20 shadow-xl',
        'gradient-outline':
          'relative bg-white text-primary-700 shadow-lg hover:shadow-xl hover:-translate-y-0.5 before:absolute before:inset-0 before:-z-10 before:rounded-lg before:bg-gradient-to-r before:from-primary-600 before:to-secondary-600 before:p-[2px]',
      },
      size: {
        default: 'h-10 px-5 py-2',
        sm: 'h-8 rounded-md px-3 text-xs',
        lg: 'h-12 rounded-lg px-8 text-base',
        xl: 'h-14 rounded-xl px-10 text-lg',
        icon: 'h-10 w-10',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
)

interface Props {
  as?: string
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'glass' | 'gradient-outline'
  size?: 'default' | 'sm' | 'lg' | 'xl' | 'icon'
  class?: string
}

const props = withDefaults(defineProps<Props>(), {
  as: 'button',
  variant: 'default',
  size: 'default'
})
</script>
