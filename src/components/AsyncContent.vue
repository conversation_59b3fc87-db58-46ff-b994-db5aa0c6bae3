<template>
  <div class="async-content">
    <!-- Loading State -->
    <LoadingSpinner
      v-if="loading"
      :text="loadingText"
      :variant="spinnerVariant"
      :fullscreen="fullscreen"
      :overlay="overlay"
    />

    <!-- Error State -->
    <div v-else-if="error" class="error-state p-6 text-center">
      <div class="max-w-md mx-auto">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-16 w-16 text-red-500 mx-auto mb-4"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
          />
        </svg>
        <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ errorTitle }}</h3>
        <p class="text-gray-600 mb-4">{{ errorMessage || error.message }}</p>
        <Button v-if="onRetry" @click="onRetry" variant="outline" size="sm">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-4 w-4 mr-2"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
            />
          </svg>
          Try Again
        </Button>
      </div>
    </div>

    <!-- Content (Success State) -->
    <div v-else class="content">
      <slot />
    </div>
  </div>
</template>

<script setup lang="ts">
import LoadingSpinner from './LoadingSpinner.vue'
import Button from './ui/Button.vue'

interface Props {
  loading?: boolean
  error?: Error | null
  loadingText?: string
  errorTitle?: string
  errorMessage?: string
  onRetry?: () => void
  spinnerVariant?: 'default' | 'primary' | 'white'
  fullscreen?: boolean
  overlay?: boolean
}

withDefaults(defineProps<Props>(), {
  loading: false,
  error: null,
  loadingText: 'Loading...',
  errorTitle: 'Something went wrong',
  spinnerVariant: 'default',
  fullscreen: false,
  overlay: false,
})
</script>

<style scoped>
.async-content {
  min-height: 200px;
  position: relative;
}

.error-state {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>