<template>
  <div class="loading-spinner-container" :class="containerClass">
    <div class="loading-spinner" :class="spinnerClass">
      <svg
        class="animate-spin"
        :width="size"
        :height="size"
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
      >
        <circle
          class="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          stroke-width="4"
        ></circle>
        <path
          class="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        ></path>
      </svg>
      <span v-if="showText" class="loading-text" :class="textClass">{{ text }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { cn } from '@/lib/utils'

interface Props {
  size?: number | string
  text?: string
  showText?: boolean
  fullscreen?: boolean
  overlay?: boolean
  variant?: 'default' | 'primary' | 'white'
}

const props = withDefaults(defineProps<Props>(), {
  size: 40,
  text: 'Loading...',
  showText: true,
  fullscreen: false,
  overlay: false,
  variant: 'default',
})

const containerClass = computed(() =>
  cn(
    'flex items-center justify-center',
    props.fullscreen && 'fixed inset-0 z-50',
    props.overlay && 'bg-black/50 backdrop-blur-sm',
    !props.fullscreen && !props.overlay && 'p-8'
  )
)

const spinnerClass = computed(() =>
  cn(
    'flex flex-col items-center gap-4',
    {
      'text-gray-600': props.variant === 'default',
      'text-primary-600': props.variant === 'primary',
      'text-white': props.variant === 'white',
    }
  )
)

const textClass = computed(() =>
  cn(
    'text-sm font-medium',
    {
      'text-gray-600': props.variant === 'default',
      'text-primary-700': props.variant === 'primary',
      'text-white': props.variant === 'white',
    }
  )
)
</script>

<style scoped>
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.loading-text {
  margin-top: 0.5rem;
}
</style>