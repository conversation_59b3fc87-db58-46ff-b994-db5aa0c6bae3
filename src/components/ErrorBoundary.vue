<template>
  <div v-if="hasError" class="error-boundary">
    <div class="min-h-screen flex items-center justify-center p-4 bg-gray-50">
      <div class="max-w-2xl w-full space-y-8 text-center">
        <!-- Error Icon -->
        <div class="flex justify-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-24 w-24 text-red-500"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
        </div>

        <!-- Error Message -->
        <div class="space-y-4">
          <h1 class="text-4xl font-bold text-gray-900">Oops! Something went wrong</h1>
          <p class="text-lg text-gray-600">
            We encountered an unexpected error while loading this page.
          </p>

          <!-- Error Details (Development Only) -->
          <div v-if="import.meta.env.DEV && error" class="mt-6 p-4 bg-red-50 rounded-lg text-left">
            <h3 class="font-semibold text-red-800 mb-2">Error Details:</h3>
            <pre class="text-sm text-red-700 overflow-x-auto whitespace-pre-wrap">{{ error }}</pre>
          </div>
        </div>

        <!-- Actions -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center mt-8">
          <Button @click="handleReload" variant="default">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 mr-2"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
              />
            </svg>
            Reload Page
          </Button>
          <Button @click="handleGoHome" variant="outline">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 mr-2"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
              />
            </svg>
            Go to Home
          </Button>
        </div>
      </div>
    </div>
  </div>
  <slot v-else />
</template>

<script setup lang="ts">
import { ref, onErrorCaptured, type Ref } from 'vue'
import { useRouter } from 'vue-router'
import Button from '@/components/ui/Button.vue'

const hasError = ref(false)
const error: Ref<Error | null> = ref(null)
const router = useRouter()

// Capture errors from child components
onErrorCaptured((err: Error) => {
  hasError.value = true
  error.value = err
  
  // Log error to console for debugging
  console.error('Error Boundary caught error:', err)
  
  // Prevent the error from propagating
  return false
})

const handleReload = () => {
  window.location.reload()
}

const handleGoHome = () => {
  hasError.value = false
  error.value = null
  router.push('/')
}
</script>

<style scoped>
.error-boundary {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
</style>