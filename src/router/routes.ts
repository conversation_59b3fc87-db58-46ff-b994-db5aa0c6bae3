/**
 * Unified route configuration
 * Single source of truth for route definitions
 */

import type { RouteConfig } from '@/types/router'
import type { RouteRecordRaw } from 'vue-router'

// Lazy-loaded view components for better performance
const HomeView = () => import('@/views/HomeView.vue')
const Attribution = () => import('@/views/Attribution.vue')
const HumanPractices = () => import('@/views/HumanPractices.vue')
const NotFound = () => import('@/views/NotFound.vue')
const Team = () => import('@/views/TeamMembers.vue')

// Project Components
const ProjectDesign = () => import('@/views/project/ProjectDesign.vue')
const ProjectOverview = () => import('@/views/project/ProjectOverview.vue')

// Wet Lab Components
const WetLabEngineering = () => import('@/views/wet-lab/Engineering.vue')
const WetLabExperiment = () => import('@/views/wet-lab/Experiment.vue')
const WetLabParts = () => import('@/views/wet-lab/Parts.vue')
const WetLabResults = () => import('@/views/wet-lab/Results.vue')
const WetLabSafety = () => import('@/views/wet-lab/Safety.vue')

// Dry Lab Components
const DryLabHardware = () => import('@/views/dry-lab/Hardware.vue')
const DryLabModel = () => import('@/views/dry-lab/Model.vue')
const DryLabSoftware = () => import('@/views/dry-lab/Software.vue')

// Get team information from environment variables
const TEAM_NAME = import.meta.env.VITE_TEAM_NAME || 'BASIS-China'
const TEAM_YEAR = import.meta.env.VITE_TEAM_YEAR || '2025'

// Create title generation function
export const createTitle = (pageName: string): string =>
  `${pageName} - ${TEAM_NAME} iGEM ${TEAM_YEAR}`

/**
 * Route configuration array
 * Contains all route definitions for router and search services
 */
export const routeConfigs: RouteConfig[] = [
  {
    path: '/',
    name: 'home',
    component: HomeView,
    meta: {
      title: 'SnaPFAS - BASIS CHINA iGEM 2025',
      category: 'home',
    },
  },
  {
    path: '/teammembers',
    name: 'team-members',
    component: Team,
    meta: {
      title: 'Team Members',
      category: 'team',
    },
  },
  {
    path: '/attribution',
    name: 'attribution',
    component: Attribution,
    meta: {
      title: 'Attribution',
      category: 'team',
    },
  },
  {
    path: '/human-practices',
    name: 'human-practices',
    component: HumanPractices,
    meta: {
      title: 'Human Practices',
      category: 'human-practices',
    },
  },
  {
    path: '/project/overview',
    name: 'project-overview',
    component: ProjectOverview,
    meta: {
      title: 'Project Overview',
      category: 'project',
    },
  },
  {
    path: '/project/design',
    name: 'project-design',
    component: ProjectDesign,
    meta: {
      title: 'Project Design',
      category: 'project',
    },
  },
  // Wet Lab Routes
  {
    path: '/wet-lab/experiment',
    name: 'wet-lab-experiment',
    component: WetLabExperiment,
    meta: {
      title: 'Wet Lab Experiment',
      category: 'wet-lab',
    },
  },
  {
    path: '/wet-lab/parts',
    name: 'wet-lab-parts',
    component: WetLabParts,
    meta: {
      title: 'Wet Lab Parts',
      category: 'wet-lab',
    },
  },
  {
    path: '/wet-lab/results',
    name: 'wet-lab-results',
    component: WetLabResults,
    meta: {
      title: 'Wet Lab Results',
      category: 'wet-lab',
    },
  },
  {
    path: '/wet-lab/safety',
    name: 'wet-lab-safety',
    component: WetLabSafety,
    meta: {
      title: 'Wet Lab Safety',
      category: 'wet-lab',
    },
  },
  {
    path: '/wet-lab/engineering',
    name: 'wet-lab-engineering',
    component: WetLabEngineering,
    meta: {
      title: 'Wet Lab Engineering',
      category: 'wet-lab',
    },
  },
  // Dry Lab Routes
  {
    path: '/dry-lab/model',
    name: 'dry-lab-model',
    component: DryLabModel,
    meta: {
      title: 'Dry Lab Model',
      category: 'dry-lab',
    },
  },
  {
    path: '/dry-lab/hardware',
    name: 'dry-lab-hardware',
    component: DryLabHardware,
    meta: {
      title: 'Dry Lab Hardware',
      category: 'dry-lab',
    },
  },
  {
    path: '/dry-lab/software',
    name: 'dry-lab-software',
    component: DryLabSoftware,
    meta: {
      title: 'Dry Lab Software',
      category: 'dry-lab',
    },
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'not-found',
    component: NotFound,
    meta: {
      title: 'Page Not Found',
      category: 'other',
    },
  },
]

/**
 * Convert route configuration to Vue Router format
 * Add complete titles to each route
 */
export const routes: RouteRecordRaw[] = routeConfigs.map(route => {
  const routeRecord: RouteRecordRaw = {
    path: route.path,
    name: route.name,
    component: route.component,
    meta: {
      ...route.meta,
      title: route.name === 'home' ? route.meta?.title : createTitle(route.meta?.title || ''),
    },
  }

  // Add children if they exist
  if (route.children && route.children.length > 0) {
    routeRecord.children = route.children.map(child => ({
      path: child.path,
      name: child.name,
      component: child.component,
      meta: {
        ...child.meta,
        title: child.name === 'home' ? child.meta?.title : createTitle(child.meta?.title || ''),
      },
    }))
  }

  // Add redirect if it exists
  if (route.redirect) {
    routeRecord.redirect = route.redirect
  }

  return routeRecord
})
