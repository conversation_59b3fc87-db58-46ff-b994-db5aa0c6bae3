/// <reference types="node" />
/// <reference types="vite/client" />

import vue from '@vitejs/plugin-vue'
import { fileURLToPath } from 'node:url'
import { defineConfig, loadEnv } from 'vite'
import vueDevTools from 'vite-plugin-vue-devtools'
// import path from 'node:path'

// https://vitejs.dev/config/
// 字符串转换为 URL 友好的 slug 格式
function stringToSlug(str: string): string {
  if (!str) return ''
  return str
    .toLowerCase()
    .trim()
    .replace(/\s+/g, '-')
    .replace(/[^\w-]+/g, '')
    .replace(/--+/g, '-')
    .replace(/^-+/, '')
    .replace(/-+$/, '')
}

/* global process, URL */

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd())
  const isProduction = mode === 'production'

  return {
    base: `/${stringToSlug(env.VITE_TEAM_NAME)}/`,
    plugins: [vue(), vueDevTools()],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
      },
    },
    // 开发服务器优化
    server: {
      hmr: {
        overlay: false,
      },
      allowedHosts: ['5b7d4d20.r15.vip.cpolar.cn', '796d0cdf.r8.cpolar.top'],
    },
    // 构建优化配置
    build: {
      // 提高构建性能
      target: 'esnext',
      modulePreload: false,
      sourcemap: false,
      // 优化代码分割
      chunkSizeWarningLimit: 1500,
      cssCodeSplit: true,
      // 使用 esbuild 进行优化
      minify: isProduction ? 'esbuild' : false,
      // rollup 构建优化
      rollupOptions: {
        output: {
          format: 'es',
          // 优化的代码分割策略
          manualChunks: {
            'vue-vendor': ['vue', 'vue-router', 'pinia'],
            'ui-vendor': ['ant-design-vue', '@ant-design/icons-vue'],
            'utils-vendor': ['@vueuse/core', 'aos', 'gsap'],
            'katex-vendor': ['katex'],
          },
          entryFileNames: 'assets/[name]-[hash].js',
          chunkFileNames: 'assets/[name]-[hash].js',
          assetFileNames: 'assets/[name]-[hash].[ext]',
        },
        // 并行化处理
        maxParallelFileOps: 5,
      },
      // 压缩选项优化
      ...(isProduction && {
        esbuild: {
          drop: ['console', 'debugger'],
          legalComments: 'none',
          minifyIdentifiers: true,
          minifySyntax: true,
          minifyWhitespace: true,
        },
      }),
    },
    // 依赖优化
    optimizeDeps: {
      include: [
        'vue',
        'vue-router',
        'pinia',
        'ant-design-vue',
        '@ant-design/icons-vue',
        '@vueuse/core',
        'aos',
        'gsap',
        'katex',
      ],
      esbuildOptions: {
        target: 'esnext',
      },
    },
  }
})
